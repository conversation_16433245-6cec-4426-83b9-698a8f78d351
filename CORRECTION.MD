# Correction

Vous serez corrigés en fonction du respect des exigences fonctionnelles de l’énoncé ainsi que la qualité du code HTML et CSS.

Quelques astuces :
- Commencez par l'implémentation de l'entête et le bas de page.
- Utilisez des classes ou sélecteurs génériques pour regrouper les règles communes. 
- Évitez des balises HTML ou règles CSS inutiles.
- Utilisez des balises sémantiquement cohérentes.
- Lisez les tests fournis pour vous aider avec la structure HTML à mettre en place.

## Grille de correction

| **Exigences**                                     | **Note** | **Points** |
| ------------------------------------------------- | :------: | :--------: |
| Entête et Pied de page                            |    0     |     2      |
| Page `index`                                      |    0     |     4      |
| Page `book`                                       |    0     |     6      |
| Page `add`                                        |    0     |     5      |
| Qualité et clarté du code HTML et CSS             |    0     |     3      |
| Bonus                                             |    0     |     1      |
| **Total**                                         |  **0**   |   **20**   |


**Note** : Des points peuvent être retirés pour une utilisation abusive de CSS dupliqué ou de règles inutiles. Également, la bonne gestion des formulaires et la validation est requise pour avoir une note maximale.

**Note** : la note maximale pour ce travail ne peut pas dépasser 20/20.


Vous serez corrigés en fonction du respect des exigences fonctionnelles de l'énoncé ainsi que la qualité du code HTML et CSS.

## Utilisation de systèmes d'intelligence artificielle générative (SIAG)

Rappel des consignes présentées dans le plan du cours en lien avec l'utilisation des SIAGs :

- **L'utilisation doit être raisonnable** : la majorité du travail doit provenir de l'étudiant.e;
- **Une utilisation jugée exagérée de SIAGs peut résulter en pénalité allant jusqu'à la note de 0 au travail**;
- **L'utilisation est clairement indiquée dans la remise des travaux.** Voir la section Fraude du plan du cours;