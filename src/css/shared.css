:root {
    /* Couleurs principales à modifier si vous le désirez */
    --primary: #333333;
    --background: #f9f9f9;
    --accent: #7d1cde;
    --text-color: #f8f9fa;
    --inverted-text-color: #333333;
    --time: #4caf50;
    --genre: #1976d2;
    --genre-background: #e3f2fd;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
   font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--primary);
    background-color: var(--background);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
}

header {
    background: var(--accent);
    color: var(--text-color);
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 100%;
}

.header-container {
    margin: 0 auto;
    /* TODO : Compléter le CSS de l'entête */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
    color: var(--text-color);
}

/* TODO : Compléter le menu de navigation */
nav ul {
    list-style: none;
    display: flex;
    gap: 1.5rem; 
    justify-content: flex-end;
}

nav a {
    color: var(--text-color);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

nav a:hover,
nav a.active {
    background-color: rgba(255, 255, 255, 0.2);
    text-decoration: underline;
}

main {
    padding: 0 2rem;
    flex: 1;
    /* TODO : limiter la taille maximale */
    max-width: 1200px; 
    margin: 2rem auto;
    width: 100%;
}

footer {
    background: var(--primary);
    color: var(--text-color);
    width: 100%;
    height: 60px;
    display: flex;
    justify-content: space-around;
    /* TODO : Compléter la mise en page du bas de page */
    align-items: center; 
    flex-direction: column; 
    padding: 1rem;
    text-align: center;
}

/* Classe fournie pour les arrondis avec ombrage */
.rounded-shadow {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-group textarea {
    /* TODO : limiter les options de changement de taille et la hauteur minimale */
    resize: vertical; 
    min-height: 100px
}

/*  TODO : Compléter les règles CSS pour les formulaires, au besoin */
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border: 2px solid var(--accent);
}

.time-inputs,
.radio-group {
    display: flex;
    gap: 1rem;
}

.time-inputs .form-group {
    flex: 1;
}

.radio-group label {
    gap: 0.5rem;
}

.submit-button {
    /* TODO : Reproduire les couleurs selon le visuel attendu */
    background-color: var(--accent);
    color: #fff;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.submit-button:hover {
    transform: translateY(-2px);
    background-color: var(--accent);
}